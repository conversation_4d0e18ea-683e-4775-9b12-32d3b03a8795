"use client";

import { create } from "zustand";
import {
  loginUser,
  registerUser,
  getCurrentUser,
  logoutUser,
} from "@/lib/api/auth";
import {
  setClientAuthState,
  clearClientAuthState,
} from "@/lib/auth/client-auth";
import type { User } from "@/types/domain/user";
import type { LoginRequest, RegisterRequest } from "@/lib/api/auth";

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitializing: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  setAuthFromLocal: (user: User) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setInitialized: () => void;
  resetInitialization: () => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>((set, get) => ({
  // Initial state - assume loading to prevent UI flash
  user: null,
  isAuthenticated: false,
  isLoading: true, // Start with loading state
  isInitializing: true,
  error: null,

  // Actions
  login: async (credentials: LoginRequest) => {
    try {
      set({ isLoading: true, error: null });

      console.log("[AuthStore] Starting login process...");
      const user = await loginUser(credentials);

      console.log("[AuthStore] Login successful, user:", user);
      set({
        user,
        isAuthenticated: true,
        isLoading: false,
        isInitializing: false,
        error: null,
      });

      // Store auth data locally for fast future checks
      setClientAuthState(user);

      console.log("[AuthStore] Auth state updated successfully");
    } catch (error: any) {
      console.error("[AuthStore] Login failed:", error);
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error.message || "Login failed",
      });
      throw error;
    }
  },

  register: async (data: RegisterRequest) => {
    try {
      set({ isLoading: true, error: null });

      console.log("[AuthStore] Starting registration process...");
      const user = await registerUser(data);

      console.log("[AuthStore] Registration successful, user:", user);
      set({
        user,
        isAuthenticated: true,
        isLoading: false,
        isInitializing: false,
        error: null,
      });

      // Store auth data locally for fast future checks
      setClientAuthState(user);
    } catch (error: any) {
      console.error("[AuthStore] Registration failed:", error);
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error.message || "Registration failed",
      });
      throw error;
    }
  },

  logout: async () => {
    try {
      set({ isLoading: true });

      console.log("[AuthStore] Starting logout process...");
      await logoutUser();

      console.log("[AuthStore] Logout successful");
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        isInitializing: false,
        error: null,
      });

      // Clear local auth data
      clearClientAuthState();
    } catch (error: any) {
      console.error("[AuthStore] Logout failed:", error);
      // Even if logout fails on server, clear local state
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        isInitializing: false,
        error: null,
      });

      // Clear local auth data even if logout failed
      clearClientAuthState();
    }
  },

  getCurrentUser: async () => {
    try {
      set({ isLoading: true, error: null });
      const user = await getCurrentUser();
      set({
        user,
        isAuthenticated: true,
        isLoading: false,
        isInitializing: false,
        error: null,
      });

      // Store auth data locally for fast future checks
      setClientAuthState(user);
    } catch (error: any) {
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        isInitializing: false,
        error: null, // Don't set error for failed auth check
      });
    }
  },

  setAuthFromLocal: (user: User) => {
    console.log("[AuthStore] Setting auth from localStorage:", user);
    set({
      user,
      isAuthenticated: true,
      isLoading: false,
      isInitializing: false,
      error: null,
    });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setInitialized: () => {
    set({ isInitializing: false });
  },

  resetInitialization: () => {
    set({
      isInitializing: true,
      user: null,
      isAuthenticated: false,
      error: null,
    });
  },
}));

// Computed values
export const useAuthComputed = () => {
  const { user, isAuthenticated } = useAuthStore();

  return {
    userDisplayName: user ? user.name || user.email : "Guest",
    isJobSeeker: user?.role === "job_seeker",
    isEmployer: user?.role === "employer",
    isAdmin: user?.role === "admin",
  };
};
