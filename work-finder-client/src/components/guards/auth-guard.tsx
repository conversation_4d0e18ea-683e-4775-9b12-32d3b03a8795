"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuthRedirect";
import { Loading } from "@/components";

interface AuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * Guard that redirects authenticated users away from auth pages
 * Use this on login, register, forgot-password pages
 */
export function AuthGuard({ children, redirectTo }: AuthGuardProps) {
  const router = useRouter();
  const { user, isAuthenticated, isInitializing } = useAuth();

  // Always call all hooks in the same order
  useEffect(() => {
    // Only redirect after initialization is complete
    if (!isInitializing && isAuthenticated && user) {
      const destination =
        redirectTo ||
        (user.role === "job_seeker"
          ? "/"
          : user.role === "employer"
          ? "/employer/dashboard"
          : user.role === "admin"
          ? "/admin"
          : "/dashboard");

      console.log(
        `[AuthGuard] Redirecting authenticated user to: ${destination}`
      );
      router.replace(destination);
    }
  }, [isAuthenticated, user, isInitializing, router, redirectTo]);

  // Always render, but conditionally show different content
  return (
    <>
      {isInitializing ? (
        <Loading fullScreen size="lg" />
      ) : isAuthenticated && user ? (
        <Loading fullScreen size="lg" />
      ) : (
        children
      )}
    </>
  );
}
