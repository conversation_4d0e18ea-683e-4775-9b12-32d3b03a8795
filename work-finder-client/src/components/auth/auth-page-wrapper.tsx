"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useFastAuth, useAuthRedirectDestination } from "@/hooks/useFastAuth";
import { Loading } from "@/components";

interface AuthPageWrapperProps {
  children: React.ReactNode;
}

/**
 * Ultra-fast wrapper for auth pages using optimized auth checking
 * Eliminates 2-3s delay by checking zustand store and localStorage efficiently
 */
export function AuthPageWrapper({ children }: AuthPageWrapperProps) {
  const router = useRouter();
  const { isAuthenticated, user, isLoading, hasChecked } = useFastAuth();
  const destination = useAuthRedirectDestination(user);

  // Handle redirect for authenticated users
  useEffect(() => {
    if (hasChecked && isAuthenticated && user) {
      console.log(
        "[AuthPageWrapper] Fast redirect for authenticated user:",
        destination
      );
      router.replace(destination);
    }
  }, [hasChecked, isAuthenticated, user, destination, router]);

  // Show loading while checking auth
  if (isLoading || !hasChecked) {
    return <Loading fullScreen size="lg" />;
  }

  // Show loading during redirect
  if (isAuthenticated && user) {
    return <Loading fullScreen size="lg" />;
  }

  // Safe to render auth form
  return <>{children}</>;
}
