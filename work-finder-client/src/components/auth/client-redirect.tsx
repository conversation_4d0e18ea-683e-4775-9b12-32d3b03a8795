"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Loading } from "@/components";

interface ClientRedirectProps {
  to: string;
  replace?: boolean;
}

export function ClientRedirect({ to, replace = true }: ClientRedirectProps) {
  const router = useRouter();

  useEffect(() => {
    if (replace) {
      router.replace(to);
    } else {
      router.push(to);
    }
  }, [router, to, replace]);

  return <Loading fullScreen size="lg" />;
}