"use client";

import { type ReactNode, useEffect } from "react";
import { NextIntlClientProvider } from "next-intl";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ThemeProvider as NextThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/atoms/sonner";
import { LanguageLoadingHandler } from "@/components/ui/atoms/language-loading";
import { useAuthStore } from "@/stores/user-store";
import { queryClient } from "@/lib/query-client";
import { Loading } from "@/components";
import { getClientAuthState } from "@/lib/auth/client-auth";

interface AppProvidersProps {
  children: ReactNode;
  locale: string;
  messages?: Record<string, unknown>;
}

function AuthInitializer({ children }: { children: ReactNode }) {
  const {
    getCurrentUser,
    isInitializing,
    setInitialized,
    user,
    isAuthenticated,
    setAuthFromLocal,
  } = useAuthStore();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // If store already has user data, no need to initialize
        if (isAuthenticated && user) {
          console.log(
            "[AuthInitializer] Store already has user data, skipping initialization"
          );
          setInitialized();
          return;
        }

        // Fast check of local auth state first
        const localAuth = getClientAuthState();

        if (localAuth.isAuthenticated && localAuth.user) {
          console.log(
            "[AuthInitializer] Found local auth, updating store without API call"
          );
          // Update store with local auth data using the new method
          setAuthFromLocal(localAuth.user);
          return;
        }

        // No local auth found and no store data, make API call as last resort
        console.log("[AuthInitializer] No local auth found, making API call");
        await getCurrentUser();
      } catch (error) {
        // Silent fail - user not authenticated
        console.log(
          "[AuthInitializer] API call failed, user not authenticated"
        );
        setInitialized();
      }
    };

    // Only initialize once and ensure we don't re-run on re-renders
    if (isInitializing) {
      initializeAuth();
    }
  }, [
    getCurrentUser,
    isInitializing,
    setInitialized,
    user,
    isAuthenticated,
    setAuthFromLocal,
  ]);

  // Show loading spinner until auth is initialized, but with shorter timeout
  if (isInitializing) {
    return <Loading fullScreen size="lg" />;
  }

  return <>{children}</>;
}

export function AppProviders({
  children,
  locale,
  messages,
}: AppProvidersProps) {
  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      <NextThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem
        disableTransitionOnChange
      >
        <QueryClientProvider client={queryClient}>
          <AuthInitializer>
            <LanguageLoadingHandler />
            {children}
            <Toaster
              position="top-right"
              richColors
              closeButton
              duration={4000}
            />
            <ReactQueryDevtools initialIsOpen={false} />
          </AuthInitializer>
        </QueryClientProvider>
      </NextThemeProvider>
    </NextIntlClientProvider>
  );
}
