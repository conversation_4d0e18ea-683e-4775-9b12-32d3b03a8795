"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components";
import { useRouter } from "next/navigation";
import { JobCard } from "@/features/jobs/components/job-card";
import { LoadingSpinner } from "@/components/ui/atoms/loading-spinner";
import { fetchFeaturedJobs, saveJob, unsaveJob } from "@/lib/api/jobs";
import type { Job } from "@/types/job";
import { toast } from "sonner";

export function FeaturedJobsSection() {
  const router = useRouter();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Responsive job display: 3 on mobile, 6 on desktop
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile on mount
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Fetch featured jobs on mount
  useEffect(() => {
    const loadFeaturedJobs = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const featuredJobs = await fetchFeaturedJobs(6);
        setJobs(featuredJobs);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load featured jobs");
        console.error("Error loading featured jobs:", err);
      } finally {
        setIsLoading(false);
      }
    };

    loadFeaturedJobs();
  }, []);

  // Handle bookmark toggle
  const handleBookmarkToggle = async (jobId: string, isBookmarked: boolean) => {
    try {
      if (isBookmarked) {
        await saveJob(jobId);
        toast.success("Job saved successfully!");
      } else {
        await unsaveJob(jobId);
        toast.success("Job removed from saved list!");
      }

      // Update local state
      setJobs(prevJobs =>
        prevJobs.map(job =>
          job.id === jobId ? { ...job, isBookmarked } : job
        )
      );
    } catch (err) {
      toast.error("Failed to update bookmark status");
      console.error("Error toggling bookmark:", err);
    }
  };

  const handleLoadMore = () => {
    router.push("/jobs");
  };

  // Show 3 jobs on mobile, 6 on desktop
  const jobsToShow = isMobile ? 3 : 6;
  const featuredJobs = jobs.slice(0, jobsToShow);

  if (error) {
    return (
      <section className="py-16 lg:py-24 bg-[#f0f5f7]">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Featured Jobs
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Know your worth and find the job that qualify your life
            </p>
          </div>
          
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <p className="text-gray-600 mb-4">
                Unable to load featured jobs at the moment.
              </p>
              <Button
                onClick={() => window.location.reload()}
                variant="primary"
                className="mr-4"
              >
                Try Again
              </Button>
              <Button
                onClick={handleLoadMore}
                variant="outline"
              >
                Browse All Jobs
              </Button>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 lg:py-24 bg-[#f0f5f7]">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Featured Jobs
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Know your worth and find the job that qualify your life
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-16">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Jobs Grid - 2 columns layout */}
        {!isLoading && featuredJobs.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8">
            {featuredJobs.map((job) => (
              <JobCard
                key={job.id}
                job={job}
                onBookmarkToggle={handleBookmarkToggle}
                className="h-full"
              />
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && featuredJobs.length === 0 && (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No featured jobs available
              </h3>
              <p className="text-gray-600 mb-6">
                Check back later for new featured job opportunities.
              </p>
              <Button
                onClick={handleLoadMore}
                variant="primary"
              >
                Browse All Jobs
              </Button>
            </div>
          </div>
        )}

        {/* Load More Button */}
        {!isLoading && featuredJobs.length > 0 && (
          <div className="text-center mt-12">
            <Button
              onClick={handleLoadMore}
              variant="primary"
              size="lg"
              className="font-medium px-8 py-3 shadow-md hover:shadow-lg transition-all duration-200"
            >
              Load More Listings
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
