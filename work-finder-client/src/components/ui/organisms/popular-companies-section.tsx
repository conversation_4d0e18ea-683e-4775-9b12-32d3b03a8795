"use client";

import { MapPin, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { LoadingSpinner } from "@/components/ui/atoms/loading-spinner";
import { useFeaturedCompanies } from "@/features/companies/hooks/use-companies";
import Image from "next/image";
import Link from "next/link";

// Fallback static data for when API fails
const fallbackCompanies = [
  {
    id: "udemy",
    name: "Ude<PERSON>",
    location: "London, UK",
    jobsCount: 15,
    featured: true,
  },
  {
    id: "stripe",
    name: "Stripe", 
    location: "London, UK",
    jobsCount: 22,
    featured: false,
  },
  {
    id: "dropbox",
    name: "Dropbox",
    location: "London, UK", 
    jobsCount: 22,
    featured: false,
  },
  {
    id: "figma",
    name: "Figma",
    location: "London, UK",
    jobsCount: 22,
    featured: false,
  },
];

export function PopularCompaniesSection() {
  const router = useRouter();
  const { companies, isLoading, error } = useFeaturedCompanies(4);

  const handleBrowseAll = () => {
    router.push("/companies");
  };

  // Use API data if available, otherwise fallback to static data
  const displayCompanies = !isLoading && companies.length > 0 ? companies : fallbackCompanies;

  return (
    <section className="py-16 lg:py-24 bg-[#f3f7fb]">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-12">
          <div className="mb-6 lg:mb-0">
            <h2 className="text-3xl lg:text-4xl font-bold text-[#202124] mb-4 font-['Jost']">
              Top Company Registered
            </h2>
            <p className="text-base text-[#696969] font-['Jost']">
              Some of the companies we&apos;ve helped recruit excellent
              applicants over the years.
            </p>
          </div>

          {/* Browse All Button */}
          <div 
            onClick={handleBrowseAll}
            className="flex items-center gap-2 text-[#1967d2] cursor-pointer hover:text-[#1557b0] transition-colors"
          >
            <span className="text-sm font-['Jost']">Browse All</span>
            <ArrowRight className="w-3 h-3" />
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-16">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Company Cards Grid */}
        {!isLoading && (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {displayCompanies.map((company) => (
              <Link
                key={company.id}
                href={`/companies/${company.id}`}
                className="bg-white rounded-lg border border-[#ecedf2] p-6 hover:shadow-lg transition-all duration-300 cursor-pointer group block"
              >
                {/* Company Logo */}
                <div className="flex justify-center mb-6">
                  <div className="w-[90px] h-[90px] bg-[#eeece8] rounded-lg flex items-center justify-center">
                    {company.logo ? (
                      <Image
                        src={company.logo}
                        alt={`${company.name} logo`}
                        width={60}
                        height={60}
                        className="object-contain rounded-lg"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gradient-to-br from-[#1967d2] to-[#1557b0] rounded-lg flex items-center justify-center text-white font-bold text-lg">
                        {company.name.charAt(0)}
                      </div>
                    )}
                  </div>
                </div>

                {/* Company Info */}
                <div className="text-center space-y-3">
                  <h3 className="text-lg font-medium text-[#202124] font-['Jost']">
                    {company.name}
                  </h3>

                  <div className="flex items-center justify-center gap-1 text-sm text-[#696969] font-['Jost']">
                    <MapPin className="w-3 h-3" />
                    <span>{company.location}</span>
                  </div>
                </div>

                {/* Open Positions Badge */}
                <div className="mt-6">
                  <div
                    className={`
                    rounded-full px-4 py-2 text-center text-sm font-['Jost'] transition-colors
                    ${
                      company.featured || company.jobsCount > 20
                        ? "bg-[#1967d2] text-white group-hover:bg-[#1557b0]"
                        : "bg-[#1967d2]/7 text-[#1967d2] group-hover:bg-[#1967d2]/15"
                    }
                  `}
                  >
                    {company.jobsCount} Open Position
                    {company.jobsCount !== 1 ? "s" : ""}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {/* Error State */}
        {error && !isLoading && (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">Unable to load companies data</p>
            <p className="text-sm text-gray-400">Showing sample companies</p>
          </div>
        )}
      </div>
    </section>
  );
}
