"use client";

import { CategoryGrid } from "@/features/categories/components/category-grid";
import { useCategories } from "@/features/categories/hooks/use-categories";
import { useMemo } from "react";

export function PopularCategoriesSection() {
  const { categories, loading, error } = useCategories();

  // Calculate stats from fetched data
  const categoryStats = useMemo(() => {
    const totalJobs = categories.reduce((sum, category) => sum + category.jobCount, 0);
    const newJobsToday = Math.floor(totalJobs * 0.15); // Estimate ~15% as new jobs today
    const liveJobs = totalJobs;

    return {
      totalJobs,
      newJobsToday,
      liveJobs,
    };
  }, [categories]);

  return (
    <section className="py-16 lg:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-[#202124] mb-4 font-jost">
            Popular Job Categories
          </h2>
          {!loading && categoryStats.liveJobs > 0 ? (
            <p className="text-lg text-[#696969] max-w-2xl mx-auto font-jost">
              {categoryStats.liveJobs} jobs live - {categoryStats.newJobsToday} added today.
            </p>
          ) : (
            <p className="text-lg text-[#696969] max-w-2xl mx-auto font-jost">
              Discover exciting job opportunities across various industries
            </p>
          )}
        </div>

        {/* Error State */}
        {error && !loading && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-red-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
              <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 font-jost">Failed to Load Categories</h3>
            <p className="text-gray-600 font-jost">{error}</p>
          </div>
        )}

        {/* Categories Grid */}
        <CategoryGrid 
          categories={categories} 
          loading={loading}
          className="max-w-6xl mx-auto"
        />

      </div>
    </section>
  );
}
