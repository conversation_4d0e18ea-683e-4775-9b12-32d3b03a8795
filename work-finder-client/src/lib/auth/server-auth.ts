import { cookies } from 'next/headers';

interface UserData {
  role?: 'job_seeker' | 'employer' | 'admin';
  id?: string;
  [key: string]: any;
}

/**
 * Server-side authentication checker
 * This runs on the server before any client-side code
 */
export async function getServerAuthState() {
  const cookieStore = await cookies();
  
  // Check for authentication token in cookies
  const accessToken = cookieStore.get('access_token')?.value ||
                     cookieStore.get('accessToken')?.value ||
                     cookieStore.get('session')?.value ||
                     cookieStore.get('authToken')?.value ||
                     cookieStore.get('token')?.value;

  const isAuthenticated = !!accessToken && accessToken.length > 0;
  
  // Try to get user data from cookies
  let userData: UserData | null = null;
  try {
    const userCookie = cookieStore.get('user')?.value || cookieStore.get('userData')?.value;
    if (userCookie) {
      userData = JSON.parse(userCookie);
    }
  } catch (error) {
    // Silent fail for malformed user data
    userData = null;
  }
  
  return {
    isAuthenticated,
    hasToken: !!accessToken,
    user: userData
  };
}

/**
 * Get role-based redirect destination
 */
function getRoleBasedRedirect(user: UserData | null, fallback: string = '/'): string {
  if (!user || !user.role) {
    return fallback;
  }
  
  switch (user.role) {
    case 'job_seeker':
      return '/';
    case 'employer':
      return '/employer/dashboard';
    case 'admin':
      return '/admin/dashboard';
    default:
      return fallback;
  }
}

/**
 * Server-side auth check for auth pages
 * Returns redirect response if user is authenticated
 */
export async function checkAuthPageAccess(intendedNext?: string) {
  const { isAuthenticated, user } = await getServerAuthState();
  
  if (isAuthenticated) {
    // If there's an intended destination, use it (with validation)
    if (intendedNext && intendedNext.startsWith('/') && !intendedNext.includes('/login') && !intendedNext.includes('/register')) {
      return { shouldRedirect: true, redirectTo: intendedNext };
    }
    
    // Otherwise, redirect based on user role
    const destination = getRoleBasedRedirect(user);
    return { shouldRedirect: true, redirectTo: destination };
  }
  
  return { shouldRedirect: false };
}