"use client";

import type { User } from "@/types/user";

const AUTH_STORAGE_KEY = 'auth_user';
const TOKEN_STORAGE_KEY = 'auth_token';

/**
 * Fast client-side auth check using localStorage
 * This is for immediate auth status check without API calls
 */
export function getClientAuthState(): { 
  isAuthenticated: boolean; 
  user: User | null; 
  hasValidSession: boolean;
} {
  try {
    // Check if we're in browser environment
    if (typeof window === 'undefined') {
      return { isAuthenticated: false, user: null, hasValidSession: false };
    }

    // Check for stored user data
    const storedUser = localStorage.getItem(AUTH_STORAGE_KEY);
    const storedToken = localStorage.getItem(TOKEN_STORAGE_KEY);
    
    if (storedUser && storedToken) {
      const user = JSON.parse(storedUser) as User;
      // Basic validation - check if user has required fields
      if (user.id && user.role) {
        return { 
          isAuthenticated: true, 
          user, 
          hasValidSession: true 
        };
      }
    }

    return { isAuthenticated: false, user: null, hasValidSession: false };
  } catch (error) {
    // If there's any error parsing stored data, assume not authenticated
    return { isAuthenticated: false, user: null, hasValidSession: false };
  }
}

/**
 * Store auth data in localStorage for fast future checks
 */
export function setClientAuthState(user: User, token?: string): void {
  try {
    if (typeof window === 'undefined') return;
    
    localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(user));
    if (token) {
      localStorage.setItem(TOKEN_STORAGE_KEY, token);
    }
  } catch (error) {
    console.warn('Failed to store auth state:', error);
  }
}

/**
 * Clear client-side auth data
 */
export function clearClientAuthState(): void {
  try {
    if (typeof window === 'undefined') return;
    
    localStorage.removeItem(AUTH_STORAGE_KEY);
    localStorage.removeItem(TOKEN_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear auth state:', error);
  }
}