/**
 * Categories API Functions
 * Using native fetch() with proper error handling
 */

import {
  handleResponse,
  getApiBaseUrl,
} from "./utils";

// Base API URL
const API_BASE_URL = getApiBaseUrl();

export interface Category {
  id: string;
  title: string;
  name: string;
  iconName: string;
  href: string;
  jobCount: number;
}

export interface CategoryJobCount {
  categoryId: string;
  jobCount: number;
}

/**
 * Get all categories with job counts
 */
export async function fetchCategories(): Promise<Category[]> {
  const response = await fetch(`${API_BASE_URL}/categories`, {
    method: "GET",
    credentials: "include",
  });

  return handleResponse<Category[]>(response);
}

/**
 * Get job count for a specific category
 */
export async function fetchCategoryJobCount(categoryId: string): Promise<CategoryJobCount> {
  const response = await fetch(`${API_BASE_URL}/categories/${categoryId}/jobs-count`, {
    method: "GET",
    credentials: "include",
  });

  return handleResponse<CategoryJobCount>(response);
}

// Re-export types for convenience  
export type { Category as ApiCategory, CategoryJobCount as ApiCategoryJobCount };