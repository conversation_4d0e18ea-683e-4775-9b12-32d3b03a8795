/**
 * Jobs API Functions
 * Using native fetch() with proper error handling
 */

import {
  handleResponse,
  getApiBaseUrl,
  buildQueryString,
  createFetchOptions,
} from "./utils";
import { fetchWithAuth } from "./with-auth";
import type { Job, JobsResponse, JobSearchParams } from "@/types/job";
import { 
  mapJobsResponse, 
  mapJobPost, 
  mapJobSearchParams,
  type BackendJobsResponse,
  type BackendJobPost,
} from "@/lib/adapters/jobs";

// Base API URL
const API_BASE_URL = getApiBaseUrl();

/**
 * Get all jobs with optional filtering
 */
export async function fetchJobs(
  params?: JobSearchParams
): Promise<JobsResponse> {
  // Map frontend params to backend params
  const backendParams = params ? mapJobSearchParams(params) : {};
  const queryString = Object.keys(backendParams).length > 0 ? buildQueryString(backendParams) : "";
  const url = `${API_BASE_URL}/jobs${queryString ? `?${queryString}` : ""}`;

  const response = await fetch(url, {
    method: "GET",
    credentials: "include",
  });

  const backendResponse = await handleResponse<BackendJobsResponse>(response);
  return mapJobsResponse(backendResponse);
}

/**
 * Get job by ID
 */
export async function fetchJob(id: string): Promise<Job> {
  const response = await fetch(`${API_BASE_URL}/jobs/${id}`, {
    method: "GET",
    credentials: "include",
  });

  const backendJob = await handleResponse<BackendJobPost>(response);
  return mapJobPost(backendJob);
}

/**
 * Search jobs with query
 */
export async function searchJobs(
  params: JobSearchParams
): Promise<JobsResponse> {
  const queryString = buildQueryString(params);
  const url = `${API_BASE_URL}/jobs/search?${queryString}`;

  const response = await fetch(url, {
    method: "GET",
    credentials: "include",
  });

  return handleResponse<JobsResponse>(response);
}

/**
 * Apply to a job
 */
export async function applyToJob(
  jobId: string,
  applicationData?: Record<string, unknown>
): Promise<void> {
  const response = await fetchWithAuth(
    `${API_BASE_URL}/jobs/${jobId}/apply`,
    createFetchOptions({
      method: "POST",
      body: applicationData ? JSON.stringify(applicationData) : undefined,
    })
  );

  if (!response.ok) {
    await handleResponse(response); // This will throw with proper error message
  }
}

/**
 * Save/bookmark a job
 */
export async function saveJob(jobId: string): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/jobs/${jobId}/save`, {
    method: "POST",
    credentials: "include",
  });

  if (!response.ok) {
    await handleResponse(response); // This will throw with proper error message
  }
}

/**
 * Unsave/unbookmark a job
 */
export async function unsaveJob(jobId: string): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/jobs/${jobId}/save`, {
    method: "DELETE",
    credentials: "include",
  });

  if (!response.ok) {
    await handleResponse(response); // This will throw with proper error message
  }
}

/**
 * Create a new job (for employers)
 */
export async function createJob(jobData: Partial<Job>): Promise<Job> {
  const response = await fetch(`${API_BASE_URL}/jobs`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify(jobData),
  });

  return handleResponse<Job>(response);
}

/**
 * Update a job (for employers)
 */
export async function updateJob(
  jobId: string,
  jobData: Partial<Job>
): Promise<Job> {
  const response = await fetch(`${API_BASE_URL}/jobs/${jobId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify(jobData),
  });

  return handleResponse<Job>(response);
}

/**
 * Delete a job (for employers)
 */
export async function deleteJob(jobId: string): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/jobs/${jobId}`, {
    method: "DELETE",
    credentials: "include",
  });

  if (!response.ok) {
    await handleResponse(response); // This will throw with proper error message
  }
}

/**
 * Get featured jobs
 */
export async function fetchFeaturedJobs(limit = 6): Promise<Job[]> {
  const response = await fetch(
    `${API_BASE_URL}/jobs?limit=${limit}&sort_by=posted_date&sort_order=DESC`,
    {
      method: "GET",
      credentials: "include",
    }
  );

  const backendResponse = await handleResponse<BackendJobsResponse>(response);
  const mappedResponse = mapJobsResponse(backendResponse);
  return mappedResponse.items;
}

/**
 * Get recent jobs
 */
export async function fetchRecentJobs(limit = 10): Promise<Job[]> {
  const response = await fetch(
    `${API_BASE_URL}/jobs?limit=${limit}&sort_by=posted_date&sort_order=DESC`,
    {
      method: "GET",
      credentials: "include",
    }
  );

  const backendResponse = await handleResponse<BackendJobsResponse>(response);
  const mappedResponse = mapJobsResponse(backendResponse);
  return mappedResponse.items;
}

// Re-export types for convenience
export type { Job, JobsResponse, JobSearchParams, JobFilters, JobApplication } from "@/types/job";
