/**
 * Job API Adapter
 * Maps backend job data structure to frontend job types
 */

import type { Job } from "@/types/job";
import type { Company } from "@/types/company";

// Backend job data structure based on the NestJS entities
export interface BackendJobPost {
  job_id: number;
  company_id: number;
  job_title: string;
  description?: string;
  location?: string;
  salary?: string;
  job_type?: 'full_time' | 'part_time' | 'contract' | 'freelance' | 'internship' | 'temporary';
  status: 'active' | 'closed' | 'draft';
  posted_date: string;
  save_count: number;
  category?: string;
  company: {
    company_id: number;
    company_name: string;
    description?: string;
    company_image?: string;
    industry?: string;
    website?: string;
  };
  applications?: any[];
  saved_by?: any[];
}

export interface BackendJobsResponse {
  jobs: BackendJobPost[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Maps backend job type to frontend employment type
 */
function mapJobType(backendType?: string): Job['employmentType'] {
  switch (backendType) {
    case 'full_time':
      return 'full-time';
    case 'part_time':
      return 'part-time';
    case 'contract':
      return 'contract';
    case 'freelance':
    case 'temporary':
      return 'contract';
    case 'internship':
      return 'internship';
    default:
      return 'full-time';
  }
}

/**
 * Parses salary string to salary object
 */
function parseSalary(salaryString?: string): Job['salary'] | undefined {
  if (!salaryString) return undefined;

  // Try to extract numbers and currency from salary string
  const currencyMatch = salaryString.match(/[\$€£¥]/);
  const currency = currencyMatch ? currencyMatch[0] : '$';
  
  const numbers = salaryString.match(/[\d,]+/g);
  if (!numbers || numbers.length === 0) return undefined;

  const cleanNumbers = numbers.map(n => parseInt(n.replace(/,/g, '')));
  
  let min: number, max: number;
  if (cleanNumbers.length === 1) {
    min = cleanNumbers[0];
    max = cleanNumbers[0];
  } else {
    min = Math.min(...cleanNumbers);
    max = Math.max(...cleanNumbers);
  }

  // Determine period based on salary range
  let period: 'hourly' | 'monthly' | 'yearly' = 'yearly';
  if (max < 1000) {
    period = 'hourly';
  } else if (max < 50000) {
    period = 'monthly';
  }

  return {
    min,
    max,
    currency,
    period,
  };
}

/**
 * Maps backend company data to frontend company type
 */
function mapCompany(backendCompany: BackendJobPost['company']): Company {
  return {
    id: backendCompany.company_id.toString(),
    name: backendCompany.company_name,
    description: backendCompany.description || '',
    logo: backendCompany.company_image,
    website: backendCompany.website,
    location: '', // Not available in backend
    industry: backendCompany.industry || 'Technology',
    size: 'medium', // Default since not available in backend
    founded: undefined,
    employees: undefined,
    benefits: [], // Not available in backend
    culture: [], // Not available in backend
    socialLinks: {},
    verified: false, // Default since not available in backend
    featured: false, // Default since not available in backend
    jobsCount: 0, // Not available in this context
    followersCount: 0, // Not available in backend
    rating: undefined,
    reviewsCount: 0, // Not available in backend
    createdAt: new Date().toISOString(), // Default
    updatedAt: new Date().toISOString(), // Default
  };
}

/**
 * Maps a single backend job to frontend job type
 */
export function mapJobPost(backendJob: BackendJobPost): Job {
  const salary = parseSalary(backendJob.salary);
  
  return {
    id: backendJob.job_id.toString(),
    title: backendJob.job_title,
    description: backendJob.description || '',
    summary: backendJob.description ? 
      backendJob.description.slice(0, 200) + (backendJob.description.length > 200 ? '...' : '') : 
      '',
    requirements: [], // Not available in backend
    benefits: [], // Not available in backend
    location: backendJob.location || 'Remote',
    workType: 'remote', // Default since not available in backend
    employmentType: mapJobType(backendJob.job_type),
    experienceLevel: 'mid', // Default since not available in backend
    salary,
    company: mapCompany(backendJob.company),
    skills: [], // Not available in backend
    tags: backendJob.category ? [backendJob.category] : [],
    postedAt: backendJob.posted_date,
    deadline: '', // Not available in backend
    applicationsCount: backendJob.applications?.length || 0,
    isBookmarked: (backendJob.saved_by?.length || 0) > 0,
    featured: false, // Default since not available in backend
    urgent: false, // Default since not available in backend
    status: backendJob.status,
  };
}

/**
 * Maps backend jobs response to frontend jobs response
 */
export function mapJobsResponse(backendResponse: BackendJobsResponse): {
  items: Job[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
} {
  return {
    items: backendResponse.jobs.map(mapJobPost),
    total: backendResponse.total,
    page: backendResponse.page,
    limit: backendResponse.limit,
    totalPages: backendResponse.totalPages,
  };
}

/**
 * Maps frontend job search params to backend API params
 */
export function mapJobSearchParams(frontendParams: {
  search?: string;
  location?: string;
  workType?: string[];
  employmentType?: string[];
  page?: number;
  limit?: number;
}): Record<string, string | number> {
  const backendParams: Record<string, string | number> = {};

  if (frontendParams.search) {
    backendParams.search = frontendParams.search;
  }

  if (frontendParams.location) {
    backendParams.location = frontendParams.location;
  }

  // Map employment type back to backend job_type
  if (frontendParams.employmentType && frontendParams.employmentType.length > 0) {
    const backendJobType = frontendParams.employmentType[0]; // Take first one for now
    switch (backendJobType) {
      case 'full-time':
        backendParams.job_type = 'full_time';
        break;
      case 'part-time':
        backendParams.job_type = 'part_time';
        break;
      case 'contract':
        backendParams.job_type = 'contract';
        break;
      case 'internship':
        backendParams.job_type = 'internship';
        break;
    }
  }

  if (frontendParams.page) {
    backendParams.page = frontendParams.page;
  }

  if (frontendParams.limit) {
    backendParams.limit = frontendParams.limit;
  }

  // Default sorting
  backendParams.sort_by = 'posted_date';
  backendParams.sort_order = 'DESC';

  return backendParams;
}