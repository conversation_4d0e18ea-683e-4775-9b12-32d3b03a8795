"use client";

import { useAuthStore } from "@/stores/user-store";
import { getClientAuthState } from "@/lib/auth/client-auth";
import { useEffect, useState } from "react";

/**
 * Fast auth hook that checks zustand store first, then localStorage
 * This prevents the 2-3s delay from API calls
 */
export function useFastAuth() {
  const { user, isAuthenticated, isInitializing, setAuthFromLocal } =
    useAuthStore();
  const [fastAuthState, setFastAuthState] = useState<{
    isAuthenticated: boolean;
    user: any;
    isLoading: boolean;
    hasChecked: boolean;
  }>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    hasChecked: false,
  });

  useEffect(() => {
    // Priority 1: Check zustand store first (fastest)
    if (isAuthenticated && user) {
      setFastAuthState({
        isAuthenticated: true,
        user,
        isLoading: false,
        hasChecked: true,
      });
      return;
    }

    // Priority 2: If store is not initializing and no user, check localStorage
    if (!isInitializing && !isAuthenticated) {
      const localAuth = getClientAuthState();

      if (localAuth.isAuthenticated && localAuth.user) {
        // Update zustand store with local data using the new method
        setAuthFromLocal(localAuth.user);

        setFastAuthState({
          isAuthenticated: true,
          user: localAuth.user,
          isLoading: false,
          hasChecked: true,
        });
      } else {
        // No auth found anywhere
        setFastAuthState({
          isAuthenticated: false,
          user: null,
          isLoading: false,
          hasChecked: true,
        });
      }
      return;
    }

    // Priority 3: Still initializing, wait briefly
    if (isInitializing) {
      const timeout = setTimeout(() => {
        // If still no auth after timeout, assume not authenticated
        if (!isAuthenticated) {
          setFastAuthState({
            isAuthenticated: false,
            user: null,
            isLoading: false,
            hasChecked: true,
          });
        }
      }, 800); // Shorter timeout

      return () => clearTimeout(timeout);
    }
  }, [user, isAuthenticated, isInitializing, setAuthFromLocal]);

  return fastAuthState;
}

/**
 * Hook for getting role-based redirect destination
 */
export function useAuthRedirectDestination(user: any) {
  if (!user) return "/";

  switch (user.role) {
    case "job_seeker":
      return "/";
    case "employer":
      return "/employer/dashboard";
    case "admin":
      return "/admin";
    default:
      return "/";
  }
}
