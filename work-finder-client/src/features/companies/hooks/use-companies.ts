"use client";

import { useState, useEffect } from "react";
import { fetchCompanies, fetchFeaturedCompanies, fetchTopCompanies } from "@/lib/api/companies";
import type { Company, CompanySearchParams } from "@/types/company";

export function useCompanies(params?: CompanySearchParams) {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    const loadCompanies = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await fetchCompanies(params);
        setCompanies(response.items);
        setTotal(response.total);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load companies");
        console.error("Error loading companies:", err);
      } finally {
        setIsLoading(false);
      }
    };

    loadCompanies();
  }, [params]);

  return {
    companies,
    isLoading,
    error,
    total,
    refetch: () => {
      const loadCompanies = async () => {
        try {
          setIsLoading(true);
          setError(null);
          const response = await fetchCompanies(params);
          setCompanies(response.items);
          setTotal(response.total);
        } catch (err) {
          setError(err instanceof Error ? err.message : "Failed to load companies");
        } finally {
          setIsLoading(false);
        }
      };
      loadCompanies();
    }
  };
}

export function useFeaturedCompanies(limit = 4) {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadFeaturedCompanies = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const featuredCompanies = await fetchFeaturedCompanies(limit);
        setCompanies(featuredCompanies);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load featured companies");
        console.error("Error loading featured companies:", err);
      } finally {
        setIsLoading(false);
      }
    };

    loadFeaturedCompanies();
  }, [limit]);

  return {
    companies,
    isLoading,
    error,
  };
}

export function useTopCompanies(limit = 4) {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTopCompanies = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const topCompanies = await fetchTopCompanies(limit);
        setCompanies(topCompanies);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load top companies");
        console.error("Error loading top companies:", err);
      } finally {
        setIsLoading(false);
      }
    };

    loadTopCompanies();
  }, [limit]);

  return {
    companies,
    isLoading,
    error,
  };
}
