"use client";

import { Card } from "@/components/ui/atoms/card";
import { MapPin } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import type { Company } from "@/types/company";

interface CompanyCardProps {
  company: Company;
  className?: string;
}

export function CompanyCard({ company, className }: CompanyCardProps) {
  const formatJobsCount = (count: number) => {
    return `${count} Open Position${count !== 1 ? 's' : ''}`;
  };

  const getCompanyInitial = (name: string) => {
    return name.charAt(0).toUpperCase();
  };

  return (
    <Link href={`/companies/${company.id}`} className="block group">
      <Card className={cn(
        "bg-white border border-gray-200 rounded-xl p-6 transition-all duration-200 hover:shadow-lg hover:border-blue-300 cursor-pointer text-center",
        className
      )}>
        {/* Company Logo */}
        <div className="flex justify-center mb-4">
          <div className="w-20 h-20 bg-gray-100 rounded-xl flex items-center justify-center">
            {company.logo ? (
              <Image
                src={company.logo}
                alt={`${company.name} logo`}
                width={48}
                height={48}
                className="object-contain rounded-lg"
              />
            ) : (
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-xl">
                {getCompanyInitial(company.name)}
              </div>
            )}
          </div>
        </div>

        {/* Company Name */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
          {company.name}
        </h3>

        {/* Location */}
        <div className="flex items-center justify-center gap-1 text-gray-500 mb-4">
          <MapPin className="w-4 h-4" />
          <span className="text-sm">{company.location}</span>
        </div>

        {/* Open Positions Button */}
        <div className="w-full">
          <div className={cn(
            "px-4 py-2 rounded-full text-sm font-medium transition-colors",
            company.jobsCount > 0 
              ? "bg-blue-500 text-white group-hover:bg-blue-600" 
              : "bg-gray-100 text-gray-500"
          )}>
            {formatJobsCount(company.jobsCount)}
          </div>
        </div>
      </Card>
    </Link>
  );
}
