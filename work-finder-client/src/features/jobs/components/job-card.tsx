"use client";

import { Card } from "@/components/ui/atoms/card";
import { BookmarkIconButton } from "@/components/ui/molecules/actions/bookmark-action";
import { MapPin } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import type { Job } from "@/types/job";

interface JobCardProps {
  job: Job;
  className?: string;
  onBookmarkToggle?: (jobId: string, isBookmarked: boolean) => Promise<void>;
}

export function JobCard({ job, className, onBookmarkToggle }: JobCardProps) {
  const handleBookmarkToggle = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onBookmarkToggle) {
      await onBookmarkToggle(job.id, !job.isBookmarked);
    }
  };

  const formatSalary = (salary: Job['salary']) => {
    if (!salary) return '15 - 25 triệu';
    const { min, max } = salary;
    return `${min} - ${max} triệu`;
  };

  return (
    <Link href={`/jobs/${job.id}`} className="block group">
      <Card className={cn(
        "relative bg-white border border-gray-200 rounded-lg p-4 transition-all duration-200 hover:shadow-sm hover:border-gray-300 cursor-pointer",
        className
      )}>
        {/* Company Logo */}
        <div className="flex items-start gap-3">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
            {job.company.logo ? (
              <Image
                src={job.company.logo}
                alt={`${job.company.name} logo`}
                width={32}
                height={32}
                className="object-contain rounded"
              />
            ) : (
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded flex items-center justify-center text-white font-semibold text-sm">
                {job.company.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>

          {/* Job Content */}
          <div className="flex-1 min-w-0">
            {/* Job Title */}
            <h3 className="text-base font-semibold text-gray-900 leading-snug mb-1 line-clamp-2">
              {job.title}
            </h3>
            
            {/* Company Name */}
            <p className="text-sm text-gray-600 mb-2">
              {job.company.name}
            </p>

            {/* Salary and Location */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-green-600 bg-green-50 px-2 py-1 rounded">
                  {formatSalary(job.salary)}
                </span>
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <MapPin className="w-3 h-3" />
                  <span>{job.location}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Bookmark Button */}
          <div className="flex-shrink-0">
            <BookmarkIconButton
              isBookmarked={job.isBookmarked}
              onToggle={handleBookmarkToggle}
              size="sm"
              variant="ghost"
              className="text-gray-400 hover:text-green-500"
            />
          </div>
        </div>

        {/* Tags (if urgent) */}
        {job.urgent && (
          <div className="mt-3 flex justify-start">
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded font-medium">
              NỔI BẬT
            </span>
          </div>
        )}
      </Card>
    </Link>
  );
}

// Export variants for different use cases
export const FeaturedJobCard = (props: JobCardProps) => (
  <JobCard {...props} className={cn("border-2 border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50", props.className)} />
);

export const UrgentJobCard = (props: JobCardProps) => (
  <JobCard {...props} className={cn("border-2 border-red-200 bg-gradient-to-br from-red-50 to-pink-50", props.className)} />
);