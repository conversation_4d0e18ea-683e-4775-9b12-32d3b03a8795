"use client";

import Link from "next/link";
import { DynamicIcon } from "@/lib/icons";
import { JobCategory } from "@/types/domain/category";

interface CategoryCardProps {
  category: JobCategory;
  className?: string;
}

export function CategoryCard({ category, className = "" }: CategoryCardProps) {
  return (
    <Link href={category.href} className={`group ${className}`}>
      <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 hover:border-blue-200">
        {/* Icon */}
        <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center mb-4 group-hover:from-blue-100 group-hover:to-blue-200 transition-all duration-300">
          <DynamicIcon 
            name={category.iconName} 
            className="w-8 h-8 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" 
          />
        </div>

        {/* Category Name */}
        <h3 className="text-lg font-semibold text-gray-900 mb-2 font-jost group-hover:text-blue-700 transition-colors duration-300">
          {category.title}
        </h3>

        {/* Job Count */}
        <p className="text-sm text-gray-600 font-jost">
          {category.jobCount === 1 
            ? `${category.jobCount} open position` 
            : `${category.jobCount} open positions`
          }
        </p>

        {/* Hover Indicator */}
        <div className="mt-4 flex items-center text-blue-600 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-0 group-hover:translate-x-1">
          <span className="text-sm font-medium font-jost">View Jobs</span>
          <svg 
            className="w-4 h-4 ml-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9 5l7 7-7 7" 
            />
          </svg>
        </div>
      </div>
    </Link>
  );
}
