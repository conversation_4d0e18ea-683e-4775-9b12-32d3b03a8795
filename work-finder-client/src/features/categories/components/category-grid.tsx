"use client";

import { CategoryCard } from "./category-card";
import { JobCategory } from "@/types/domain/category";

interface CategoryGridProps {
  categories: JobCategory[];
  className?: string;
  loading?: boolean;
}

function CategorySkeleton() {
  return (
    <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm animate-pulse">
      {/* Icon skeleton */}
      <div className="w-16 h-16 bg-gray-200 rounded-xl mb-4"></div>
      
      {/* Title skeleton */}
      <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
      
      {/* Job count skeleton */}
      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
    </div>
  );
}

export function CategoryGrid({ categories, className = "", loading = false }: CategoryGridProps) {
  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
        {Array.from({ length: 9 }).map((_, index) => (
          <CategorySkeleton key={index} />
        ))}
      </div>
    );
  }

  if (!categories || categories.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="w-16 h-16 bg-gray-100 rounded-xl mx-auto mb-4 flex items-center justify-center">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2 font-jost">No Categories Available</h3>
        <p className="text-gray-600 font-jost">Categories will appear here once they are loaded.</p>
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {categories.map((category) => (
        <CategoryCard 
          key={category.id} 
          category={category}
          className="w-full"
        />
      ))}
    </div>
  );
}
