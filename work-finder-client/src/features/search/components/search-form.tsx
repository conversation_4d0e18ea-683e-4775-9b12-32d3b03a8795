"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Search, MapPin, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/atoms/button";
import { Input } from "@/components/ui/atoms/input";
import {
  Form,
  FormField,
  FormItem,
  FormControl,
} from "@/components/ui/atoms/form";
import { cn } from "@/lib/utils";
import {
  jobSearchSchema,
  type JobSearchFormData,
} from "@/lib/validations/search";

// Popular locations for autocomplete
const POPULAR_LOCATIONS = [
  "Ho Chi Minh City",
  "Hanoi",
  "Da Nang",
  "Can Tho",
  "Hai Phong",
  "Bien Hoa",
  "Nha Trang",
  "Hue",
  "Vung Tau",
];

interface SearchFormProps {
  onSearch: (data: JobSearchFormData) => void;
  className?: string;
  isLoading?: boolean;
  defaultValues?: Partial<JobSearchFormData>;
}

export function SearchForm({
  onSearch,
  className,
  isLoading = false,
  defaultValues = {},
}: SearchFormProps) {
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [locationQuery, setLocationQuery] = useState(
    defaultValues.location || ""
  );

  const form = useForm<JobSearchFormData>({
    resolver: zodResolver(jobSearchSchema),
    defaultValues: {
      keywords: defaultValues.keywords || "",
      location: defaultValues.location || "",
    },
  });

  const handleSubmit = (data: JobSearchFormData) => {
    onSearch(data);
  };

  const filteredLocations = POPULAR_LOCATIONS.filter((location) =>
    location.toLowerCase().includes(locationQuery.toLowerCase())
  );

  return (
    <div className={cn("w-full", className)}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          {/* Simple Search Bar - 2 inputs + 1 button */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-2">
            <div className="flex items-center gap-0">
              {/* Keywords Field */}
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="keywords"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <Search className="h-5 w-5 text-gray-400" />
                          </div>
                          <Input
                            {...field}
                            placeholder="Job title, keywords, or company"
                            className="pl-12 h-12 border-0 focus:ring-0 focus:border-0 text-base placeholder:text-gray-500 bg-transparent"
                            disabled={isLoading}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Vertical Divider */}
              <div className="w-px h-8 bg-gray-200 mx-2"></div>

              {/* Location Field */}
              <div className="flex-1 relative">
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <MapPin className="h-5 w-5 text-gray-400" />
                          </div>
                          <Input
                            {...field}
                            placeholder="City or postcode"
                            className="pl-12 h-12 border-0 focus:ring-0 focus:border-0 text-base placeholder:text-gray-500 bg-transparent"
                            disabled={isLoading}
                            value={locationQuery}
                            onChange={(e) => {
                              const value = e.target.value;
                              setLocationQuery(value);
                              field.onChange(value);
                              setShowLocationSuggestions(value.length > 0);
                            }}
                            onFocus={() => setShowLocationSuggestions(locationQuery.length > 0)}
                            onBlur={() => {
                              setTimeout(() => setShowLocationSuggestions(false), 200);
                            }}
                          />

                          {/* Location Suggestions */}
                          {showLocationSuggestions && filteredLocations.length > 0 && (
                            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-auto">
                              {filteredLocations.map((location) => (
                                <button
                                  key={location}
                                  type="button"
                                  className="w-full text-left px-4 py-3 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none flex items-center gap-2 text-sm"
                                  onClick={() => {
                                    setLocationQuery(location);
                                    field.onChange(location);
                                    setShowLocationSuggestions(false);
                                  }}
                                >
                                  <MapPin className="h-4 w-4 text-gray-400" />
                                  {location}
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              {/* Search Button */}
              <div className="flex-shrink-0 ml-2">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="h-12 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    "Find Jobs"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
