# SearchForm Component

A comprehensive, responsive search form component for job searching with modern UI/UX patterns, accessibility features, and TypeScript support.

## Features

### ✅ Core Functionality
- **React Hook Form Integration**: Form validation and state management with Zod schema
- **TypeScript Support**: Full type safety with proper interfaces
- **Responsive Design**: Desktop, tablet, and mobile optimized layouts
- **Real-time Validation**: Client-side validation with error states
- **Loading States**: Visual feedback during search operations

### 🎨 UI/UX Features
- **Modern Design**: Clean, professional interface with rounded corners and shadows
- **Location Autocomplete**: Dropdown suggestions for Vietnam cities
- **Job Category Selection**: Predefined categories with searchable dropdown
- **Active Filters Display**: Visual badges showing current search filters
- **Clear Filters**: Easy removal of individual or all filters
- **Advanced Filters**: Expandable section for additional search options

### ♿ Accessibility Features
- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard support for all interactions
- **Focus Management**: Logical tab order and focus indicators
- **Role Attributes**: Semantic HTML with proper ARIA roles
- **Screen Reader Support**: Descriptive text for assistive technologies

### 📱 Mobile Optimization
- **Mobile-First Design**: Optimized for touch interactions
- **Dialog-Based Filters**: Mobile-friendly filter interface
- **Responsive Grid**: Adaptive layout for different screen sizes
- **Touch-Friendly**: Larger touch targets and spacing

## Components

### 1. SearchForm (Desktop/Tablet)
Main search form component with horizontal layout.

```tsx
import { SearchForm } from "@/features/search/components/search-form";

<SearchForm
  onSearch={(data) => console.log(data)}
  isLoading={false}
  showAdvancedFilters={true}
  defaultValues={{
    keywords: "",
    location: "",
    category: ""
  }}
/>
```

### 2. MobileSearchForm
Mobile-optimized version with dialog-based filters.

```tsx
import { MobileSearchForm } from "@/features/search/components/mobile-search-form";

<MobileSearchForm
  onSearch={(data) => console.log(data)}
  isLoading={false}
  defaultValues={{
    keywords: "",
    location: "",
    category: ""
  }}
/>
```

## Props Interface

```typescript
interface SearchFormProps {
  onSearch: (data: JobSearchFormData & { category?: string }) => void;
  className?: string;
  isLoading?: boolean;
  showAdvancedFilters?: boolean; // Desktop only
  defaultValues?: Partial<JobSearchFormData & { category?: string }>;
}

interface JobSearchFormData {
  keywords: string;
  location: string;
}
```

## Data Structure

### Search Output
```typescript
{
  keywords: string;        // Job title, keywords, or company
  location: string;        // City or "remote"
  category?: string;       // Job category (optional)
}
```

### Available Categories
- Technology
- Marketing
- Design
- Sales
- Finance
- Healthcare
- Education
- Engineering
- Customer Service
- Human Resources

### Location Suggestions
Predefined Vietnam cities:
- Hồ Chí Minh
- Hà Nội
- Đà Nẵng
- Cần Thơ
- Hải Phòng
- Nha Trang
- Huế
- Vũng Tàu

## Styling

### Design System Integration
- Uses project's color scheme (`#1967d2` primary blue)
- Consistent with existing button variants
- Follows Tailwind CSS utility patterns
- Responsive breakpoints: `sm:`, `md:`, `lg:`, `xl:`

### Custom Classes
```css
/* Active filter badges */
.bg-blue-50.text-blue-700.border-blue-200    /* Keywords filter */
.bg-green-50.text-green-700.border-green-200 /* Location filter */
.bg-purple-50.text-purple-700.border-purple-200 /* Category filter */

/* Form inputs */
.h-14.rounded-xl.border-gray-200.focus:border-blue-500 /* Standard input height and styling */
```

## Usage Examples

### Basic Implementation
```tsx
function JobSearchPage() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = async (data) => {
    setIsLoading(true);
    try {
      const results = await searchJobs(data);
      // Handle results
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SearchForm
      onSearch={handleSearch}
      isLoading={isLoading}
      showAdvancedFilters={true}
    />
  );
}
```

### With Default Values
```tsx
<SearchForm
  onSearch={handleSearch}
  defaultValues={{
    keywords: "React Developer",
    location: "Hồ Chí Minh",
    category: "technology"
  }}
/>
```

### Responsive Implementation
```tsx
function ResponsiveSearch() {
  return (
    <>
      {/* Desktop/Tablet */}
      <div className="hidden md:block">
        <SearchForm onSearch={handleSearch} showAdvancedFilters={true} />
      </div>
      
      {/* Mobile */}
      <div className="md:hidden">
        <MobileSearchForm onSearch={handleSearch} />
      </div>
    </>
  );
}
```

## Testing

### Demo Page
Visit `/search-demo` to test the component with:
- Form validation
- Location autocomplete
- Category selection
- Filter management
- Loading states
- Responsive behavior

### Key Test Cases
1. **Form Validation**: Submit empty form, invalid inputs
2. **Location Autocomplete**: Type partial city names
3. **Category Selection**: Select and clear categories
4. **Filter Management**: Add/remove individual filters
5. **Responsive Design**: Test on different screen sizes
6. **Accessibility**: Navigate with keyboard only
7. **Loading States**: Test with simulated API delays

## Dependencies

- `react-hook-form`: Form state management
- `@hookform/resolvers/zod`: Zod integration
- `zod`: Schema validation
- `lucide-react`: Icons
- `@radix-ui/*`: UI primitives
- `tailwindcss`: Styling
- `class-variance-authority`: Component variants

## File Structure

```
src/features/search/components/
├── search-form.tsx           # Main desktop/tablet component
├── mobile-search-form.tsx    # Mobile-optimized component
└── README.md                 # This documentation

src/lib/validations/
└── search.ts                 # Zod schemas and types

src/app/
└── search-demo/
    └── page.tsx              # Demo and testing page
```

## Performance Considerations

- **Debounced Location Search**: Prevents excessive API calls
- **Memoized Filter Lists**: Static data cached for performance
- **Lazy Loading**: Advanced filters loaded on demand
- **Optimized Re-renders**: Minimal state updates with React Hook Form
- **Bundle Size**: Tree-shakeable imports and code splitting

## Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+
- **Accessibility**: WCAG 2.1 AA compliant
- **Responsive**: Supports viewport widths from 320px to 1920px+
