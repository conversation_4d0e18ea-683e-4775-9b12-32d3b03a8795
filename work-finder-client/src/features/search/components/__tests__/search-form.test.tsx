import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchForm } from '../search-form';

// Mock the form components
jest.mock('@/components/ui/atoms/form', () => ({
  Form: ({ children, ...props }: any) => <form {...props}>{children}</form>,
  FormField: ({ children, render }: any) => render({ field: { onChange: jest.fn(), value: '' } }),
  FormItem: ({ children }: any) => <div>{children}</div>,
  FormControl: ({ children }: any) => <div>{children}</div>,
}));

jest.mock('@/components/ui/atoms/input', () => ({
  Input: (props: any) => <input {...props} />,
}));

jest.mock('@/components/ui/atoms/button', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
}));

jest.mock('@/components/ui/atoms/select', () => ({
  Select: ({ children, onValueChange }: any) => (
    <div data-testid="select" onClick={() => onValueChange('technology')}>
      {children}
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children }: any) => <div>{children}</div>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

jest.mock('@/components/ui/atoms/badge', () => ({
  Badge: ({ children }: any) => <span data-testid="badge">{children}</span>,
}));

describe('SearchForm', () => {
  const mockOnSearch = jest.fn();

  beforeEach(() => {
    mockOnSearch.mockClear();
  });

  it('renders the search form with all fields', () => {
    render(<SearchForm onSearch={mockOnSearch} />);

    expect(screen.getByPlaceholderText('Job title, keywords, or company')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('City or remote')).toBeInTheDocument();
    expect(screen.getByText('All categories')).toBeInTheDocument();
    expect(screen.getByText('Find Jobs')).toBeInTheDocument();
  });

  it('shows loading state when isLoading is true', () => {
    render(<SearchForm onSearch={mockOnSearch} isLoading={true} />);

    expect(screen.getByText('Searching...')).toBeInTheDocument();
  });

  it('displays default values when provided', () => {
    const defaultValues = {
      keywords: 'React Developer',
      location: 'Ho Chi Minh',
      category: 'technology'
    };

    render(<SearchForm onSearch={mockOnSearch} defaultValues={defaultValues} />);

    expect(screen.getByDisplayValue('React Developer')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Ho Chi Minh')).toBeInTheDocument();
  });

  it('shows location suggestions when typing', async () => {
    const user = userEvent.setup();
    render(<SearchForm onSearch={mockOnSearch} />);

    const locationInput = screen.getByPlaceholderText('City or remote');
    await user.type(locationInput, 'Ho');

    await waitFor(() => {
      expect(screen.getByText('Hồ Chí Minh')).toBeInTheDocument();
    });
  });

  it('handles form submission with search data', async () => {
    const user = userEvent.setup();
    render(<SearchForm onSearch={mockOnSearch} />);

    const keywordsInput = screen.getByPlaceholderText('Job title, keywords, or company');
    const locationInput = screen.getByPlaceholderText('City or remote');
    const submitButton = screen.getByText('Find Jobs');

    await user.type(keywordsInput, 'Frontend Developer');
    await user.type(locationInput, 'Ha Noi');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith({
        keywords: 'Frontend Developer',
        location: 'Ha Noi',
        category: undefined
      });
    });
  });

  it('shows active filters when form has values', async () => {
    const user = userEvent.setup();
    render(<SearchForm onSearch={mockOnSearch} />);

    const keywordsInput = screen.getByPlaceholderText('Job title, keywords, or company');
    await user.type(keywordsInput, 'React');

    await waitFor(() => {
      expect(screen.getByText('Active filters:')).toBeInTheDocument();
      expect(screen.getByTestId('badge')).toBeInTheDocument();
    });
  });

  it('clears individual filters when remove button is clicked', async () => {
    const user = userEvent.setup();
    render(<SearchForm onSearch={mockOnSearch} />);

    const keywordsInput = screen.getByPlaceholderText('Job title, keywords, or company');
    await user.type(keywordsInput, 'React');

    await waitFor(() => {
      const removeButton = screen.getByRole('button', { name: /clear/i });
      expect(removeButton).toBeInTheDocument();
    });
  });

  it('shows advanced filters section when enabled', () => {
    render(<SearchForm onSearch={mockOnSearch} showAdvancedFilters={true} />);

    expect(screen.getByText('Advanced Filters')).toBeInTheDocument();
    expect(screen.getByText('Advanced filters coming soon...')).toBeInTheDocument();
  });

  it('applies custom className when provided', () => {
    const { container } = render(
      <SearchForm onSearch={mockOnSearch} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles keyboard navigation for location suggestions', async () => {
    const user = userEvent.setup();
    render(<SearchForm onSearch={mockOnSearch} />);

    const locationInput = screen.getByPlaceholderText('City or remote');
    await user.type(locationInput, 'Ho');

    await waitFor(() => {
      expect(screen.getByText('Hồ Chí Minh')).toBeInTheDocument();
    });

    // Test Escape key to close suggestions
    await user.keyboard('{Escape}');
    
    await waitFor(() => {
      expect(screen.queryByText('Hồ Chí Minh')).not.toBeInTheDocument();
    });
  });

  it('selects location from suggestions when clicked', async () => {
    const user = userEvent.setup();
    render(<SearchForm onSearch={mockOnSearch} />);

    const locationInput = screen.getByPlaceholderText('City or remote');
    await user.type(locationInput, 'Ho');

    await waitFor(() => {
      const suggestion = screen.getByText('Hồ Chí Minh');
      expect(suggestion).toBeInTheDocument();
    });

    const suggestion = screen.getByText('Hồ Chí Minh');
    await user.click(suggestion);

    await waitFor(() => {
      expect(locationInput).toHaveValue('Hồ Chí Minh');
    });
  });

  it('handles category selection', async () => {
    const user = userEvent.setup();
    render(<SearchForm onSearch={mockOnSearch} />);

    const categorySelect = screen.getByTestId('select');
    await user.click(categorySelect);

    // The mock will automatically set the category to 'technology'
    const submitButton = screen.getByText('Find Jobs');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith(
        expect.objectContaining({
          category: 'technology'
        })
      );
    });
  });
});
