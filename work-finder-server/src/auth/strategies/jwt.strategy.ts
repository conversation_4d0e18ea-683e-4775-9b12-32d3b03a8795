import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

export interface JwtPayload {
  sub: number;
  username: string;
  role: string;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      jwtFromRequest: (req): string | null => {
        let token: string | null = null;
        if (req && req.cookies) {
          token = req.cookies['access_token'] || null;
        }
        if (!token) {
          const bearerToken = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
          token = bearerToken || null;
        }
        return token;
      },
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') || 'your-secret-key',
    });
  }

  async validate(payload: JwtPayload) {
    return {
      user_id: payload.sub,
      username: payload.username,
      role: payload.role,
    };
  }
}
