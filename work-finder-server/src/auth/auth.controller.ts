import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  Request,
  Response,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  Response as ExpressResponse,
  Request as ExpressRequest,
} from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';
import { ResponseMessage } from '../common/interceptors/response.interceptor';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @ResponseMessage('User registered successfully')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({
    status: 201,
    description: 'User registered successfully',
    schema: {
      example: {
        success: true,
        status: 201,
        data: {
          user: {
            user_id: 1,
            username: 'john_doe',
            full_name: 'John Doe',
            email: '<EMAIL>',
            role: 'job_seeker',
          },
        },
        message: 'User registered successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 409, description: 'Username or email already exists' })
  async register(
    @Body() registerDto: RegisterDto,
    @Response() res: ExpressResponse,
  ) {
    const authResult = await this.authService.register(registerDto);

    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: undefined,
      path: '/',
    };

    res.cookie('access_token', authResult.access_token, {
      ...cookieOptions,
      maxAge: 60 * 60 * 1000, // 1 hour
    });

    res.cookie('refresh_token', authResult.refresh_token, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    return res.json({
      success: true,
      status: 201,
      data: {
        user: authResult.user,
      },
      message: 'User registered successfully',
      timestamp: new Date().toISOString(),
    });
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ResponseMessage('User logged in successfully')
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({
    status: 200,
    description: 'User logged in successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: {
          user: {
            user_id: 1,
            username: 'john_doe',
            full_name: 'John Doe',
            email: '<EMAIL>',
            role: 'job_seeker',
          },
        },
        message: 'User logged in successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(
    @Body() loginDto: LoginDto,
    @Request() req: ExpressRequest,
    @Response() res: ExpressResponse,
  ) {
    const authResult = await this.authService.login(req.user);

    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: undefined,
      path: '/',
    };

    res.cookie('access_token', authResult.access_token, {
      ...cookieOptions,
      maxAge: 60 * 60 * 1000, // 1 hour
    });

    res.cookie('refresh_token', authResult.refresh_token, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    return res.json({
      success: true,
      status: 200,
      data: {
        user: authResult.user,
      },
      message: 'User logged in successfully',
      timestamp: new Date().toISOString(),
    });
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({
    status: 200,
    description: 'Token refreshed successfully',
    schema: {
      example: {
        success: true,
        status: 200,
        data: { message: 'Token refreshed successfully' },
        message: 'Token refreshed successfully',
        timestamp: '2024-01-01T00:00:00.000Z',
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refresh(
    @Request() req: ExpressRequest,
    @Response() res: ExpressResponse,
  ) {
    const refreshToken = req.cookies['refresh_token'];
    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        status: 401,
        message: 'Refresh token not found',
        timestamp: new Date().toISOString(),
        error: {
          code: 'UNAUTHORIZED',
          details: 'Refresh token not found in cookies',
        },
      });
    }

    const result = await this.authService.refreshToken(refreshToken);

    res.cookie('access_token', result.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: undefined,
      path: '/',
      maxAge: 60 * 60 * 1000, // 1 hour
    });

    return res.json({
      success: true,
      status: 200,
      data: { message: 'Token refreshed successfully' },
      message: 'Token refreshed successfully',
      timestamp: new Date().toISOString(),
    });
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({ status: 200, description: 'User logged out successfully' })
  @ApiBearerAuth()
  async logout(@CurrentUser() user: any, @Response() res: ExpressResponse) {
    await this.authService.logout(user.user_id);
    const clearCookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      domain: undefined,
      path: '/',
    };

    res.clearCookie('access_token', clearCookieOptions);
    res.clearCookie('refresh_token', clearCookieOptions);

    return res.json({
      success: true,
      status: 200,
      data: { message: 'User logged out successfully' },
      message: 'User logged out successfully',
      timestamp: new Date().toISOString(),
    });
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Current user profile' })
  @ApiBearerAuth()
  async getProfile(@CurrentUser() user: any, @Request() req: ExpressRequest) {
    return { user };
  }
}
